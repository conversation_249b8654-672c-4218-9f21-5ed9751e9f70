System Requirements Document

Taxi Station Passenger Waiting Time Prediction Agent
Document Version: 1.0
Date: July 24, 2025
Status: Draft

Table of Contents

1.
Introduction
2. Agent Overview
Functional Requirements
3.
4. Non-Functional Requirements
5. Agent Architecture Requirements
6. Data Requirements
7.
Interface Requirements
8. Hardware Requirements
9. Software Requirements
10. Security Requirements
11. Performance Requirements
12. Implementation Requirements
13. Acceptance Criteria

1. Introduction
1.1 Purpose

This document specifies the agent requirements for the Taxi Station Passenger Waiting Time
Prediction Agent. The agent aims to provide real-time predictions of passenger waiting
times at taxi stations using video analytics, machine learning, and multi-source data fusion.
1.2 Scope

The agent shall:

 Monitor taxi station queues using existing and new surveillance cameras


Analyze video feeds to count passengers and available taxis
Predict waiting times using queuing theory and machine learning
Provide real-time updates via API interfaces





1.3 Definitions and Acronyms

 API: Application Programming Interface
 AI: Artificial Intelligence
 GPU: Graphics Processing Unit


RTSP: Real Time Streaming Protocol
FPS: Frames Per Second
ROI: Region of Interest





2. Agent Overview

The Taxi Station Passenger Waiting Time Prediction Agent is a comprehensive solution that
intelligence, and real-time data analytics to predict
leverages computer vision, artificial

passenger waiting times at taxi stations. The agent processes video streams from multiple
cameras,
integrates external data sources (weather, events), and employs advanced
algorithms to provide accurate waiting time estimates.

3. Functional Requirements
3.1 Video Data Collection
FR-3.1.1 The agent shall collect video streams from multiple surveillance cameras positioned
at:

 Queue entrance points (to monitor passenger arrivals)
Boarding areas (to monitor passenger departures)

Taxi arrival/departure zones (to monitor taxi availability)
Adjacent roads (to monitor traffic conditions)





FR-3.1.2 The agent shall support both existing cameras and new camera installations. They
are all collected from the IOC system.
3.2 Video Analytics and Processing
FR-3.2.1 The agent shall perform real-time object detection to identify:







Passengers in queue
Available taxis
Vehicle traffic patterns

FR-3.2.2 The agent shall track objects across multiple frames to ensure accurate counting.
3.3 Data Integration
FR-3.3.1 The agent shall integrate weather data from external APIs including:







Current weather conditions
Temperature
Precipitation levels

FR-3.3.2 The agent shall maintain an event calendar for special events that may impact
demand.
FR-3.3.3 The agent shall analyze traffic congestion levels from video feeds.
3.4 Waiting Time Prediction
FR-3.4.1 The agent shall calculate waiting times using:

 Queuing theory models
 Machine learning correction models
 Historical data patterns

FR-3.4.2 The agent shall update predictions every minute.
FR-3.4.3 The agent shall adjust predictions based on:



Special events
 Weather conditions
Traffic congestion

Time of day/week patterns



3.5 Data Output and APIs
FR-3.5.1 The agent shall provide RESTful API endpoints for:



Current waiting time queries

 Historical waiting time data
Agent status information




Event notifications

FR-3.5.2 The agent shall support JSON data format for all API responses.

4. Non-Functional Requirements
4.1 Reliability
NFR-4.1.1 The agent shall maintain 99.5% uptime during operational hours.
NFR-4.1.2 The agent shall include redundancy for critical components.
4.2 Scalability
NFR-4.2.1 The agent shall support horizontal scaling to accommodate additional taxi
stations.
NFR-4.2.2 The agent shall handle at least 20 camera feeds per station simultaneously.
4.3 Maintainability
NFR-4.3.1 The agent shall provide comprehensive logging and monitoring capabilities.
NFR-4.3.2 The agent shall support remote diagnostics and updates.
4.4 Usability
NFR-4.4.1 The API shall be well-documented with clear examples.

5. Agent Architecture Requirements
5.1 Architecture Overview

The agent shall employ a layered architecture consisting of:

1. Data Collection Layer: Connect video stream or video record from IOC
2. Processing Layer: Video analytics and ML inference engines
3. Data Integration Layer: External data interfaces and fusion
4. Business Logic Layer: Prediction algorithms and decision logic
5. Service Layer: API endpoints and data distribution

6. Data Requirements
6.1 Data Collection
DR-6.1.1 The agent shall collect and process:





Passenger count data (per minute)
Taxi availability data (per minute)

 Weather data (every 5 minutes)


Traffic congestion metrics (per minute)

6.2 Data Storage
DR-6.2.1 The agent shall store:



Processed metrics for at least 1 year

 Model training data indefinitely

DR-6.2.2 The agent shall use time-series databases for metric storage.
6.3 Data Processing
DR-6.3.1 The agent shall clean and validate all input data.
DR-6.3.2 The agent shall handle missing data gracefully.

7. Interface Requirements
7.1 Camera Interfaces

IR-7.1.1 The agent shall support IP cameras with RTSP protocol.
IR-7.1.2 The agent shall handle H.264/H.265 video codecs.
7.2 External Data Interfaces
IR-7.2.1 The agent shall integrate with weather APIs using HTTPS.
IR-7.2.2 The agent shall support webhook notifications for events.
7.3 API Interfaces
IR-7.3.1 The agent shall provide RESTful APIs over HTTPS.
IR-7.3.2 The agent shall implement API authentication using tokens.
IR-7.3.3 The agent shall support rate limiting for API calls.

8. Hardware Requirements
8.1 Server Requirements
HR-8.1.1 Central Processing Server: （ to be discussed )





CPU: ≥ 2 x 64 cores
RAM: ≥256GB

 GPU: 2x Huawei Ascend 910b


Storage: ≥2TB SSD for cache and databases

 Network: ≥1Gbps ethernet
Redundant power supplies


8.2 Camera Requirements
HR-8.3.1 Surveillance Cameras:





Resolution: ≥1080p
Low-light/IR capability

 Weather-resistant (IP66 rating)
 Network connectivity

9. Software Requirements
9.1 Operating System
SR-9.1.1 Servers shall run Linux (Ubuntu 22. or above ).
9.2 Database Systems
SR-9.3.1 PostgreSQL or GaussDB for structured data.
9.3 Container Platform
SR-9.3.1 Docker for application containerization.

10. Security Requirements
10.1 Data Security
SEC-10.1.1 All video data shall remain within the local network.
SEC-10.1.2 API communications shall use TLS 1.2 or higher.
SEC-10.1.3 Sensitive data shall be encrypted at rest.
10.2 Access Control
SEC-10.2.1 The agent shall implement role-based access control.
SEC-10.2.2 API access shall require authentication tokens.
10.3 Network Security
SEC-10.3.1 The agent shall be deployed in a segregated network.

SEC-10.3.2 Firewalls shall restrict unnecessary access.
SEC-10.3.3 VPN shall be used for remote management.

11. Performance Requirements
11.1 Response Time
PR-11.1.1 Video processing latency shall not exceed 1 minute.
PR-11.1.2 API response time shall be under 500ms for 95% of requests.
PR-11.1.3 Prediction updates shall complete within 1 minute cycles.
11.2 Accuracy
PR-11.2.1 Passenger counting accuracy shall exceed 85%.
PR-11.2.2 Waiting time predictions shall have Mean Absolute Error ≤ 5 minutes.
11.3 Capacity
PR-11.3.1 The agent shall process at least 10 video streams concurrently per station.
PR-11.3.2 The agent shall handle 100 API requests per minute.

12. Implementation Requirements
12.1 Development Standards
IM-12.1.1 Code shall follow industry best practices and coding standards.
IM-12.1.2 All code shall be version controlled using Git.
12.2 Testing Requirements
IM-12.2.1 Unit test coverage shall exceed 80%.
IM-12.2.2 Integration testing shall cover all API endpoints.
IM-12.2.3 Performance testing shall validate all performance requirements.
12.3 Documentation
IM-12.3.1 Technical documentation shall be maintained for all components.
IM-12.3.2 API documentation shall include examples and use cases.
IM-12.3.3 Operations manual shall be provided for agent administrators.

13. Acceptance Criteria
13.1 Functional Acceptance









All functional requirements are implemented and tested
Agent accurately counts passengers and taxis
Predictions are generated every minute
APIs return correct data formats

13.2 Performance Acceptance







Agent meets all performance benchmarks
Accuracy metrics are within specified ranges
Response times meet requirements

13.3 Operational Acceptance





Agent runs continuously for 5 days without failures
All monitoring and alerting components are functional

 Documentation is complete and accurate

Appendices

Appendix A: API Specification Details

[To be added]
Appendix B: Sample Data Formats

[To be added]
Appendix C: Deployment Checklist

[To be added]

