Kai Tak Sports Park AI
Staff Recruitment Plan

2025.06

01

Trends and
Challenges

02 AI agent

scenario design

CONTENTS

03 Estimate

investment

04

Implementation
recommendation

DeepSeek ignites the world with high performance, low
threshold, and open source, bringing opportunities for
industry transformation

R1

O1

R1 32B

O1-mini

V3

)

%
(
e

l
i
t

n
e
c
r
e
P

/

y
c
a
r
u
c
c
A

96.3

96.6

90.693.4

75.7

71.5

58.7

62.1

60.0 59.1

79.8

79.2

72.6

63.6

39.2

97.3 96.4

94.3

90.0 90.2

90.8 91.8

87.485.2

88.5

100 million
users

• DeepSeek R1: 2 weeks
• ChatGPT: 2 months

49.2

48.9

41.6 42.0

36.8

AIME
(103+% )

Codeforce
(64+%)

GPQA
(20+%)

Math
(7%)

MMLU
(20%)

Debug
(16+%)

200+
large enterprises

Covering multiple fields such as
automotive, electric power, healthcare,
and telecommunications , including global
tech giants such as Microsoft and Nvidia

Algorithm innovation and engineering innovation significantly improve LLM training efficiency
and chipset communication efficiency, greatly reducing the cost of using large models.

4

DeepSeek will fully empower enterprise employees
and business processes

Task assistant

Content generation

Employee assistant

Intelligent Code Assistant  |  Meeting minutes assistant

Operation Ticket Generation  |  Procurement Contract Generation

1. LLM + Prompt

3. LLM+SFT

Knowledge Q&A

Core process agent

Intelligent Customer Service  |  HR Policy Q&A

 Equipment Status assessment | Energy Optimization

2. LLM+RAG

4. LLM + Post-training

DeepSeek | Pangu | ……

AI Platform

Computing Backbone

Knowledge Q&A | Understanding |  Text
generation | Logic reasoning | Code
generation

Data engineering  | Model
training/inference/deployment | Model
management

 Atlas 800 | DCS A3000 | MA Site |
HCS+MA

LLM：Large Language Model

RAG：Retrieval-augmented Generation

SFT：Supervised Fine-Tuning

Professional assistant

Industry experts

5

AI is not just DeepSeek…

Scientific
Intelligence

Professional
Forecast

Intelligent
Decision-making

Auxiliary
Assistant

Computer
Vision

Analysis
Review

Large molecular model |
Large meteorological model

Regression prediction |
Anomaly detection

Cross-modal search |
Cross-modal generation |
Show and tell

Content generation |
Content understanding

Classification |
Segmentation | Detection

Content generation |
Content understanding

AI promotes campus operation maintenance upgrade

6

Pain Points Suffered by Kai Tak Sports Park
Operation Team

Low efficiency in doc retrieval

Heavy workload in O&M

Slow response to audience
issues

• Massive documentation​

​: Over 100k

pre-construction docs (e.g., WLAN
Hardware Installation & Maintenance
Guide – 2,328 pages) at Kai Tak
Sports Park.

•

​: Frequent doc

Inefficient retrieval​
switching + manual filtering of
irrelevant content → slow access to
critical info.

•

•

​: Intrusion

​High false alarm rate​
detection false alarm >90% (inability
to distinguish staff from outsiders
via line-based detection).

​:

​Overwhelming alarm volume​
Massive daily alerts (e.g., 28,946
alerts in one day) → overwhelming
staff workload; Intrusion alarm
handling rate＜12.5%; VAS alarm
resolved tickets take >12 hours on
average.

• ​

​Manual service dependency​
Manual operations as the primary
service model.

​:

• Customer issues cannot be

resolved immediately: In cases of
issues such as missing persons or
items, the on-site team faces long
delays in receiving feedback, resulting
in delayed responses.

7

​
​
Initial Agent Scenarios

lower the risk
of intrusion
and damage

Save time in
finding
answers

Reduce long
waits for IOC
reports

Ease traffic
and avoid
accidents

Improve public
satisfaction

People Intrusion
Alarm Agent

Document
Retrieval Agent

Report Generation
Agent

Taxi Flow Alarm
Agent

Customer Service
Agent

On-premises deployment

Cloud deployment

9

People Intrusion Alarm Agent

Challenge:
Intrusion detection false alarm >90%
Intrusion alarm handling rate＜ 12.5%

Agent Functions

• train model to verify if
intrusion alerts are real
• capture IOC alert info &

photos at the time

• use the model to check if the

alerts are false

• auto-clear the alert if it's

false

• auto-generate alert report

Future Development

• Train model to do more

verification for other false
alarms

10

Document Retrieval Agent

Challenge:
On average each employee spend >1h per day in
finding proper documents to get the answers

Agent Functions

• Builds RAG-based knowledge

base

• Imports documents,
categorizes & tags

• Provides search answers with

source references

• Support doc, xls，ppt, pdf，
md, txt，file size < 15M

• 1G for each person
• 100G for company

Future Development

• Train department specified
expert agent for complex
scenarios

11

Report Generation Agent

Challenge:
On average, IOC staffs will take more than 2 days in
preparing a formal report.

Agent Functions

• Integrates with IOC systems
• Adds metadata for AI

interpretation

• Agent auto-generates reports
using data and use provided
context

• Output as downloadable

reports

Future Development

• Connect to more systems
• Extent to internet search

12

Taxi Flow Alarm Agent

Challenge:
Taxi Overcrowded before big events and lack of flow
controlling system

Agent Functions

• Photos of the taxi stand
taken via multiple cameras
• Crowdedness score of the taxi

stand analyzed using VLM
(maximum 100%)

• Periodic push of crowdedness

score to the IOC

• IOC decides whether to notify
the front gate to control
traffic flow based on the
situation

Future Development

• Extent to the temp Taxi

station by adding cameras
13

Customer Service Agent

Challenge:
There is NO online customer service in the APP,
which is quite important for customer satisfaction

Agent Functions

•
•

•

•

Connect with the customer app
Customers can ask questions
online via text
Agent will auto-reply based on
the knowledge base
Including 10 scenarios (e.g.,
Lost and Found, Location Inquiry,
Complaint Follow-up, Emergency
Assistance), for specific
scenario, a work order will be
generated and relevant staff will
be notified for follow-up.

Future Development

• Connect to Call Center to

support voice call

14

AI DEMO CASE

15

Agent Scenarios & KPI

Agent

Scenario & KPI

Technology Stack

People Intrusion Alarm Agent

False alarm reduction ≥85%

YOLO v7 +  Qwen 2.5-VL
validation & tuning

Document Retrieval Agent

Personal DB>1G， Company
DB>100G

DeepSeek R1 + RAG engine

Report Generation Agent

Report automation ≥80%

DeepSeek R1 + LLM-SQL

Customer Service Agent

Response time < 2s

Qwen 3-32B

Taxi Flow Alarm Agent

Flow response time <30s

YOLO v7 +  Qwen 2.5-VL
validation & tuning

16

Transformation Path with AI Empowerment

People
Intrusion
Alarm Agent

Document Retrieval
Agent

Report
Generation Agent

Customer Service
Agent

Taxi Flow Alarm
Agent

Model Layer

Deepseek-R1 | Qwen3 | Qwen2.5 | YOLO v7

Computing Base

8 x

4U

• H a r d w a r e   L a y e r :   H u a w e i   A I   i n f r a s t r u c t u r e ,
a n   i n t e g r a t e d   c a b i n e t   h o u s i n g   A t l a s   8 0 0
A I   s e r v e r s .

• M o d e l   L a y e r :   V i s u a l   r e c o g n i t i o n   m o d e l s
( Y O L O   v 7   a n d   Q w e n 2 . 5 - V L - 7 2 B )   a n d   l a r g e
l a n g u a g e   m o d e l s   ( D e e p s e e k - R 1 - D i s t i l l -
Q w e n - 3 2 B   a n d   Q w e n 3 - 3 2 B ) ,   p r o v i d i n g   r o b u s t
i n f e r e n c e   a n d   d e c i s i o n - m a k i n g
c a p a b i l i t i e s .

• A p p l i c a t i o n   L a y e r :   T h e   i n i t i a l   b a t c h   o f
f i v e   a g e n t   a p p l i c a t i o n   s c e n a r i o s   s e r v e .

Feature
Form

Description
4U Server

CPU

NPU

4 * ARM 48 Cores

8 * NPU Cards

Memory

HBM Vol：512/256G HBM；32xDDR4 DRAM

Intra-Network

8NPU HCCS Full Connect @ 392GB/s

Output Network

NPU Output / 8 * 200G RoCE

18

Implementation Roadmap

Months 0–1

Months 2–4

Months 5–7

Months 8–10

AI infrastructure
delivery, server
room upgrades,
data pipeline
integration

People Intrusion
Alarm Agent;
Document
Retrieval Agent;
validate 85%
false alarm
reduction

Deploy Report,
Search, and
Customer Service
Agents; Taxi Flow
Alarm Agent;
conduct internal
staff training

Evaluate KPI
results;
establish
reusable
templates for
rapid replication

Future AI Cases for Stadium Campus Use

 Smart Facility
Operations &
Maintenance​

Energy & Resource
Optimization​

Safety & Emergency
Enhancement​

Spectator
Experience
Enhancement​

Staff & Workflow
Optimization​

AI-based Decision
Support​

•

•

•

​Predictive Facility
Maintenance
​Energy Optimization
Management
​Automated Cleaning
Management

• …

• Dynamic Energy

Scheduling

• Smart Inventory
Management

• …

•

Intelligent Surveillance
& Alerts

• Emergency Response
• Crowd Flow Control​
• …

• WiFi Experience
Detection and
optimization

• Personalized Service

•

Intelligent Shift
Scheduling
• Repair Ticket
Prioritization

Guidance

• Smart Parking
Navigation​

• …

• Employee Assistant
• …

• Space Utilization

Analytics

• Asset Replacement
Recommendations

• …

21

​
​
​
​
​
​
​
AI Vision

Kai Tak Sports Park’s AI staff pioneers

intelligent service, leveraging tech to

create seamless experiences, optimize

events, and unite athletes, artists, and

communities, positioning Hong Kong as a

global sports innovation leader.

Investment Structure & Platform Reusability

Item

Investment Item

Description

QTY

AI Server

AI Inference server, with 8*NPU Cards, Onsite Premier  Technical
Support_36Month(s)

infrastructure

ARM Server

Rack server 8*2.5 inch hard disk 2U standard chassis, Onsite Premier
Technical Support_36Month(s)

Network equipment

Datacenter AI Switch with 25Gbps bandwith, Onsite Premier  Technical
Support_36Month(s)

People Intrusion Alarm Agent

YOLO v7 +  Qwen 2.5-VL validation & tuning, False alarm reduction ≥85%

Document Retrieval Agent

DeepSeek R1 + RAG engine, Personal DB>1G， Company DB>100G

Application

Report Generation Agent

DeepSeek R1 + LLM-SQL, Report automation ≥80%

Customer Service Agent

Qwen 3-32B, Response time < 2s

Taxi Flow Alarm Agent

YOLO v7 +  Qwen 2.5-VL validation & tuning, Flow response time <30s

System integration IOC System Upgrades

IOC Software Update and Addition of New Features
- GUI Development for new features
- Automated Alert Trigger: Push photos and videos based on automatic alert
triggering.
- Cyclic task scheduling development

Cloud Resource

Cloud Resource for Customer
Service Agent

The cost of cloud resources for three years, and the need to continue
investing every year after three years.

Total

3

4

4

1

1

1

1

1

1

3

24

